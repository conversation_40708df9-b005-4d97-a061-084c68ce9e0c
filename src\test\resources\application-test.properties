# Test Configuration for UAV Docking Management System
spring.application.name=UAV-Docking-Management-System-Test

# Use H2 in-memory database for testing
spring.datasource.url=jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
spring.datasource.username=sa
spring.datasource.password=
spring.datasource.driver-class-name=org.h2.Driver

# JPA/Hibernate settings for testing
spring.jpa.hibernate.ddl-auto=create-drop
spring.jpa.database-platform=org.hibernate.dialect.H2Dialect
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.format_sql=false

# Disable security for testing (or configure test security)
spring.security.user.name=test
spring.security.user.password=test
spring.security.user.roles=ADMIN

# Logging configuration for tests
logging.level.org.hibernate.SQL=WARN
logging.level.org.hibernate.type.descriptor.sql.BasicBinder=WARN
logging.level.org.springframework.web=WARN
logging.level.com.zaxxer.hikari=WARN
logging.level.com.example.uavdockingmanagementsystem=INFO

# Test-specific settings
spring.test.database.replace=none
spring.jpa.defer-datasource-initialization=true

# H2 Console (useful for debugging tests)
spring.h2.console.enabled=true
spring.h2.console.path=/h2-console

# Redis configuration for testing (disable Redis auto-configuration)
spring.data.redis.repositories.enabled=false
spring.autoconfigure.exclude=org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration,org.springframework.boot.autoconfigure.data.redis.RedisRepositoriesAutoConfiguration,org.springframework.boot.autoconfigure.graphql.GraphQlAutoConfiguration,org.springframework.boot.autoconfigure.graphql.servlet.GraphQlWebMvcAutoConfiguration

# Disable GraphQL for tests
spring.graphql.graphiql.enabled=false

# Disable WebSocket for tests to avoid connection issues
spring.websocket.enabled=false

# Disable mail for tests
spring.mail.host=localhost
spring.mail.port=25
spring.mail.username=
spring.mail.password=
spring.mail.properties.mail.smtp.auth=false
spring.mail.properties.mail.smtp.starttls.enable=false

# Disable actuator endpoints for tests
management.endpoints.enabled-by-default=false
management.endpoint.health.enabled=true
